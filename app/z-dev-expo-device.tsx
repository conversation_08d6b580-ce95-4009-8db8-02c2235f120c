import React, { useState, useEffect } from 'react'

import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform,
    ScrollView
} from 'react-native'

import { router } from 'expo-router'

import axiosApi from '../axios/axiosApi'

import { useSafeAreaInsets } from 'react-native-safe-area-context'

import { HugeiconsIcon } from '@hugeicons/react-native'
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons'

import * as Device from 'expo-device'


export default function ZDevExpoDevice() {
    const insets = useSafeAreaInsets()
    const [deviceInfo, setDeviceInfo] = useState({
        deviceName: '',
        deviceType: '',
        brand: '',
        manufacturer: '',
        modelName: '',
        osName: '',
        osVersion: ''
    })

    useEffect(() => {

        const info = {
            deviceName: Device.deviceName || '未知',
            deviceType: Device.deviceType?.toString() || '未知',
            brand: Device.brand || '未知',
            manufacturer: Device.manufacturer || '未知',
            modelName: Device.modelName || '未知',
            osName: Device.osName || '未知',
            osVersion: Device.osVersion || '未知'
        }
        setDeviceInfo(info)

        console.log('设备名称:', Device.deviceName)
        console.log('设备类型:', Device.deviceType)
        console.log('品牌:', Device.brand)
        console.log('制造商:', Device.manufacturer)
        console.log('型号名称:', Device.modelName)
        console.log('操作系统:', Device.osName)
        console.log('系统版本:', Device.osVersion)



        // axiosApi.get('/myip')
        //     .then(response => {
        //         console.log(response)
        //     })
        //     .catch(error => {
        //         console.error("Error fetching data: ", error)
        //     })



        axiosApi.post('/myip', {
            // deviceInfo: info
            deviceName: Device.deviceName || '未知设备',
        })
            .then(response => {
                console.log(response)
            })
            .catch(error => {
                console.error("Error fetching data: ", error)
            })



    }, [])

    const handleTopLeftBack = () => {
        router.back()
    }




    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}> Device </Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <ScrollView style={styles.content}>
                        <View style={styles.deviceInfoContainer}>
                            <Text style={styles.title}>设备信息</Text>

                            <View style={styles.infoItem}>
                                <Text style={styles.label}>设备名称:</Text>
                                <Text style={styles.value}>{deviceInfo.deviceName}</Text>
                            </View>

                            <View style={styles.infoItem}>
                                <Text style={styles.label}>设备类型:</Text>
                                <Text style={styles.value}>{deviceInfo.deviceType}</Text>
                            </View>

                            <View style={styles.infoItem}>
                                <Text style={styles.label}>品牌:</Text>
                                <Text style={styles.value}>{deviceInfo.brand}</Text>
                            </View>

                            <View style={styles.infoItem}>
                                <Text style={styles.label}>制造商:</Text>
                                <Text style={styles.value}>{deviceInfo.manufacturer}</Text>
                            </View>

                            <View style={styles.infoItem}>
                                <Text style={styles.label}>型号名称:</Text>
                                <Text style={styles.value}>{deviceInfo.modelName}</Text>
                            </View>

                            <View style={styles.infoItem}>
                                <Text style={styles.label}>操作系统:</Text>
                                <Text style={styles.value}>{deviceInfo.osName}</Text>
                            </View>

                            <View style={styles.infoItem}>
                                <Text style={styles.label}>系统版本:</Text>
                                <Text style={styles.value}>{deviceInfo.osVersion}</Text>
                            </View>
                        </View>
                    </ScrollView>
                </View>
            </ImageBackground>
        </>

    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,

        // justifyContent: 'center',
        // alignItems: 'center',
        // backgroundColor: '#f40',
    },

    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },


    content: {
        flex: 1,
        padding: 16,
        // backgroundColor: 'white',
    },

    deviceInfoContainer: {
        backgroundColor: '#fff',
        borderColor: '#000',
        borderWidth: 1,

        padding: 20,
        margin: 10,
    },

    title: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 20,
        textAlign: 'center',
    },

    infoItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,

        // borderBottomWidth: 1,
        // borderBottomColor: '#f0f0f0',
    },

    label: {
        fontSize: 16,
        fontWeight: '600',
        color: '#555',
        flex: 1,
    },

    value: {
        fontSize: 16,
        color: '#333',
        flex: 2,
        textAlign: 'right',
        fontWeight: '400',
    },
})
